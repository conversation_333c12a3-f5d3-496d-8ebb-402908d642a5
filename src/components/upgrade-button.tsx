'use client'

import React from 'react'
import { Gem } from 'lucide-react'
import { useModalStore } from '@/store/use-modal-store'
import { GradientButton } from '@/components/ui/extended'
import { VariantProps } from 'class-variance-authority'
import { buttonVariants } from '@/components/ui/button'

interface UpgradeButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  variant?: 'default' | 'premium'
  showIcon?: boolean
  size?: 'default' | 'sm' | 'lg' | 'icon' | null | undefined
  responsive?: boolean
}

/**
 * UpgradeButton component
 *
 * A button for upgrading to premium that opens the pricing modal.
 * Supports a premium variant with a gradient background.
 * Can be responsive to hide text on smaller screens.
 */
export function UpgradeButton({
  variant = 'default',
  showIcon = true,
  size = 'default',
  responsive = false,
  children = 'Upgrade',
  className,
  ...props
}: UpgradeButtonProps) {
  const { onOpen } = useModalStore()

  // Handle button click to open upgrade modal
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    onOpen('upgrade', {
      feature: 'projects', // Default feature for general upgrade
      featureName: 'premium features',
      upgradeMessage:
        'Upgrade your plan to unlock more features and higher limits',
    })
  }

  // Use GradientButton for premium variant
  if (variant === 'premium') {
    return (
      <GradientButton
        onClick={handleClick}
        size={size}
        className={className}
        responsive={responsive}
        leftIcon={showIcon ? <Gem className='h-4 w-4' /> : undefined}
        {...props}
      >
        {children}
      </GradientButton>
    )
  }

  // Use regular GradientButton with primary styling for non-premium variant
  return (
    <GradientButton
      onClick={handleClick}
      size={size}
      gradientType='primary'
      className={className}
      responsive={responsive}
      leftIcon={showIcon ? <Gem className='h-4 w-4' /> : undefined}
      {...props}
    >
      {children}
    </GradientButton>
  )
}
