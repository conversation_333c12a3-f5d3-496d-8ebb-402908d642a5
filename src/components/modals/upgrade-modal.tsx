'use client'

import React, { useState } from 'react'
import { Gem, Crown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useModalStore } from '@/store/use-modal-store'
import {
  BillingToggle,
  PricingTable,
} from '@/app/(dashboard)/billing/_components'
import { getUIPlans } from '@/lib/plan-utils'

export function UpgradeModal() {
  const { onClose, onOpen, data } = useModalStore()
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'annual'>(
    'annual'
  )

  // Extract data from modal store
  const {
    featureName = 'this feature',
    upgradeMessage,
    currentPlan = 'free',
  } = data

  // Consistent icon for all features (matching upgrade button)
  const getFeatureIcon = () => {
    return <Gem className='h-4 w-4 text-orange-500' />
  }

  // Get plans using centralized logic
  const allPlans = getUIPlans(billingPeriod)

  // Always show Basic and Premium plans (except for premium users who see contact sales)
  const filteredPlans =
    currentPlan === 'premium'
      ? []
      : allPlans.filter(
          plan => plan.name === 'Basic' || plan.name === 'Premium'
        )

  const handleViewAllPlans = () => {
    onClose()
    onOpen('pricing')
  }

  return (
    <div className='space-y-6'>
      {/* Header - Compact */}
      <div className='text-center space-y-3'>
        <div className='flex justify-center items-center gap-2'>
          {getFeatureIcon()}
          <p className='text-muted-foreground'>
            {upgradeMessage || `Unlock ${featureName} with a premium plan`}
          </p>
        </div>
      </div>

      {/* Current Plan Badge */}
      <div className='flex justify-center'>
        <div className='inline-flex items-center px-3 py-1 rounded-full bg-muted text-muted-foreground text-sm'>
          Current: {currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1)}{' '}
          Plan
        </div>
      </div>

      {/* Premium users - Contact sales */}
      {currentPlan === 'premium' && (
        <div className='border rounded-lg p-6 text-center bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950/20 dark:to-blue-950/20'>
          <div className='mb-4'>
            <Crown className='h-12 w-12 text-purple-500 mx-auto mb-3' />
            <h3 className='text-xl font-semibold text-foreground mb-2'>
              Already on Premium!
            </h3>
            <p className='text-muted-foreground'>
              Need more? Contact our sales team for enterprise solutions.
            </p>
          </div>
          <Button
            onClick={() => window.open('mailto:<EMAIL>', '_blank')}
            className='bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700'
          >
            Contact Sales Team
          </Button>
        </div>
      )}

      {/* Billing Toggle and Pricing Table for Free/Basic users */}
      {filteredPlans.length > 0 && (
        <div className='space-y-6'>
          {/* Billing Toggle */}
          <div className='flex justify-center'>
            <BillingToggle
              activePeriod={billingPeriod}
              onChange={setBillingPeriod}
            />
          </div>

          {/* Pricing Table - Centered layout */}
          <div className='flex justify-center w-full pt-2'>
            <div className='[&>div]:!px-0 [&>div]:!max-w-none'>
              <PricingTable
                plans={filteredPlans}
                billingPeriod={billingPeriod}
              />
            </div>
          </div>
        </div>
      )}

      {/* Footer Actions */}
      <div className='flex flex-col gap-3 pt-4 border-t mt-6'>
        <Button onClick={handleViewAllPlans} variant='ghost' className='w-full'>
          View All Plans & Pricing
        </Button>

        <Button onClick={onClose} variant='outline' className='w-full'>
          Maybe Later
        </Button>
      </div>
    </div>
  )
}
