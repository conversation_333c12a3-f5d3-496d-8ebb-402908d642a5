import { plans } from '@/config/constants'
import type { Plan } from '@/types/pricing'

/**
 * Format storage value - show in GB if >= 1024MB
 */
function formatStorage(storageMB: number): string {
  if (storageMB >= 1024) {
    const storageGB = Math.round(storageMB / 1024)
    return `${storageGB}GB`
  }
  return `${storageMB}MB`
}

/**
 * Convert plans from constants.ts to UI Plan format
 */
export function getUIPlans(
  billingPeriod: 'monthly' | 'annual' = 'annual'
): Plan[] {
  // Get unique plan names (free, basic, premium)
  const uniquePlanNames = Array.from(new Set(plans.map(plan => plan.name)))

  return uniquePlanNames.map(planName => {
    // Find the appropriate plan for the billing period
    let targetPlan = plans.find(
      plan => plan.name === planName && plan.period === billingPeriod
    )

    // If no plan found for the billing period, use the available plan (for monthly plans when requesting annual)
    if (!targetPlan) {
      targetPlan = plans.find(plan => plan.name === planName) || plans[0]
    }

    const isForever = targetPlan.period === 'forever'
    const isMonthlyPlan = targetPlan.period === 'monthly'

    // Calculate pricing based on requested billing period
    let displayPrice: string
    let annualPrice: string | undefined
    let annualSavings: string | undefined

    if (isForever) {
      displayPrice = '$0'
    } else if (billingPeriod === 'annual') {
      if (targetPlan.period === 'annual') {
        // Use the actual annual plan pricing
        displayPrice = `$${targetPlan.price}`
      } else {
        // Calculate annual pricing from monthly plan with 20% discount
        const monthlyPrice = targetPlan.price
        const annualTotal = monthlyPrice * 12
        const discountedAnnual = Math.round(annualTotal * 0.8) // 20% discount
        displayPrice = `$${discountedAnnual}`
        annualSavings = `Save $${annualTotal - discountedAnnual}`
      }
    } else {
      // Monthly billing
      displayPrice = `$${targetPlan.price}`
      if (isMonthlyPlan) {
        // Calculate what the annual would be
        const annualTotal = targetPlan.price * 12
        const discountedAnnual = Math.round(annualTotal * 0.8) // 20% discount
        annualPrice = `$${discountedAnnual}`
        annualSavings = `Save $${annualTotal - discountedAnnual}`
      }
    }

    // For annual billing, use annual limits if available, otherwise scale monthly limits
    let effectiveLimits = targetPlan.limits
    if (billingPeriod === 'annual' && targetPlan.period === 'monthly') {
      // Scale up monthly limits for annual display (since it's annual billing)
      effectiveLimits = {
        ...targetPlan.limits,
        projects: targetPlan.limits.projects * 12,
        videoExports: targetPlan.limits.videoExports * 12,
        aiImages: targetPlan.limits.aiImages * 12,
        storage: targetPlan.limits.storage * 12,
      }
    }

    return {
      name: planName.charAt(0).toUpperCase() + planName.slice(1), // Capitalize first letter
      price: displayPrice,
      annualPrice,
      annualSavings,
      billingPeriod: isForever ? '/forever' : `billed ${billingPeriod}ly`,
      description: getPlanDescription(planName),
      highlightTag: planName === 'premium' ? 'Most Popular' : null,
      videoCreation: effectiveLimits.projects,
      videoDownloads: effectiveLimits.videoExports,
      maxVideoLength: getMaxVideoLength(planName),
      resolution: getResolution(planName),
      aiImageGenerator: `${effectiveLimits.aiImages} images`,
      storage: formatStorage(effectiveLimits.storage),
      publishToYoutube: planName === 'premium',
      proVoices: planName === 'premium',
      voiceCloning: false, // Not available in any plan currently
      curationSupport: false, // Not available in any plan currently
      buttonText: 'Get started',
      buttonVariant:
        planName === 'free' ? ('outline' as const) : ('default' as const),
    }
  })
}

function getPlanDescription(planName: string): string {
  switch (planName) {
    case 'free':
      return 'For individuals'
    case 'basic':
      return 'For small to medium-sized businesses'
    case 'premium':
      return 'For growing businesses and teams'
    default:
      return 'For users'
  }
}

function getMaxVideoLength(planName: string): string {
  switch (planName) {
    case 'free':
      return '1 minute'
    case 'basic':
      return '3 minutes'
    case 'premium':
      return '5 minutes'
    default:
      return '1 minute'
  }
}

function getResolution(planName: string): string {
  switch (planName) {
    case 'free':
    case 'basic':
      return 'HD'
    case 'premium':
      return 'Full HD'
    default:
      return 'HD'
  }
}

/**
 * Get plan by name from constants
 */
export function getPlanByName(planName: string) {
  return plans.find(p => p.name.toLowerCase() === planName.toLowerCase())
}

/**
 * Get plan by name and period from constants
 */
export function getPlanByNameAndPeriod(
  planName: string,
  period?: 'monthly' | 'annual'
) {
  if (!period) {
    return getPlanByName(planName)
  }
  return plans.find(
    p => p.name.toLowerCase() === planName.toLowerCase() && p.period === period
  )
}

/**
 * Get plan limits by name and period
 */
export function getPlanLimits(planName: string, period?: 'monthly' | 'annual') {
  const plan = getPlanByNameAndPeriod(planName, period)
  return plan?.limits || plans[0].limits // Default to free plan limits
}
