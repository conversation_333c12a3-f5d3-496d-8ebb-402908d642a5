/**
 * Feature Gating System
 *
 * Centralized system for managing feature access based on subscription plans
 * and usage limits. Integrates with existing usage API and subscription system.
 */

import { plans } from '@/config/constants'
import { UsageData } from '@/hooks/use-usage'
import { SubscriptionData } from '@/hooks/useSubscription'

// Feature types that can be gated
export type GatedFeature =
  | 'projects'
  | 'videoExports'
  | 'aiImages'
  | 'teamMembers'
  | 'voiceRegenerations'
  | 'videoDuration'
  | 'videoPublishing'

// Feature check result
export interface FeatureCheckResult {
  allowed: boolean
  reason?: string
  current?: number
  limit?: number
  planName?: string
  upgradeRequired?: boolean
}

/**
 * Get plan configuration by name
 */
export function getPlanByName(planName: string) {
  return (
    plans.find(plan => plan.name.toLowerCase() === planName.toLowerCase()) ||
    plans[0]
  ) // Default to free
}

/**
 * Get current plan from subscription data
 */
export function getCurrentPlan(subscription: SubscriptionData | null) {
  if (!subscription || subscription.status !== 'active') {
    return plans[0] // Free plan
  }
  return getPlanByName(subscription.plan)
}

/**
 * Check if a feature is allowed based on subscription and usage
 */
export function checkFeatureAccess(
  feature: GatedFeature,
  subscription: SubscriptionData | null,
  usage?: UsageData
): FeatureCheckResult {
  const currentPlan = getCurrentPlan(subscription)

  // Handle special cases first
  switch (feature) {
    case 'videoPublishing':
      if (currentPlan.gatedFeatures?.videoPublishing) {
        return {
          allowed: false,
          reason: 'Video publishing is only available on Premium plan',
          planName: currentPlan.name,
          upgradeRequired: true,
        }
      }
      return { allowed: true }

    case 'videoExports':
      if (currentPlan.gatedFeatures?.videoExport) {
        return {
          allowed: false,
          reason: 'Video exports are not available on Free plan',
          planName: currentPlan.name,
          upgradeRequired: true,
        }
      }
      // Check usage limits for non-free plans
      if (usage && currentPlan.limits.videoExports > 0) {
        const used = usage.usage.videoExports.used
        const exportLimit = currentPlan.limits.videoExports
        if (used >= exportLimit) {
          return {
            allowed: false,
            reason: `Video export limit reached (${used}/${exportLimit})`,
            current: used,
            limit: exportLimit,
            planName: currentPlan.name,
            upgradeRequired: true,
          }
        }
      }
      return { allowed: true }

    case 'voiceRegenerations':
      // Voice regeneration tracking is now handled via project API in useVoiceRegeneration hook
      // This case should not be used - kept only for type compatibility
      return {
        allowed: false,
        reason:
          'Voice regeneration check should use useVoiceRegeneration hook instead',
      }

    case 'videoDuration':
      // This is handled differently - we return the max allowed duration
      return {
        allowed: true,
        limit: currentPlan.limits.videoDuration,
        planName: currentPlan.name,
      }

    default:
      // Handle standard usage-based features
      if (!usage) {
        return { allowed: true } // Allow if no usage data available
      }

      const usageKey = feature as keyof typeof usage.usage
      const featureUsage = usage.usage[usageKey]

      if (!featureUsage) {
        return { allowed: true }
      }

      const used = featureUsage.used
      const maxLimit = featureUsage.max

      if (used >= maxLimit) {
        return {
          allowed: false,
          reason: `${feature} limit reached (${used}/${maxLimit})`,
          current: used,
          limit: maxLimit,
          planName: currentPlan.name,
          upgradeRequired: true,
        }
      }

      return { allowed: true, current: used, limit: maxLimit }
  }
}

/**
 * Get user-friendly feature names for UI display
 */
export function getFeatureDisplayName(feature: GatedFeature): string {
  const displayNames: Record<GatedFeature, string> = {
    projects: 'Projects',
    videoExports: 'Video Exports',
    aiImages: 'AI Images',
    teamMembers: 'Team Members',
    voiceRegenerations: 'Voice Regenerations',
    videoDuration: 'Video Duration',
    videoPublishing: 'Video Publishing',
  }

  return displayNames[feature] || feature
}

/**
 * Get upgrade message for a specific feature
 */
export function getUpgradeMessage(
  feature: GatedFeature,
  planName: string
): string {
  const featureName = getFeatureDisplayName(feature)

  if (planName === 'free') {
    return `Upgrade to Basic or Premium to unlock ${featureName}`
  }

  if (planName === 'basic' && feature === 'videoPublishing') {
    return `Upgrade to Premium to unlock ${featureName}`
  }

  return `Upgrade your plan to get more ${featureName}`
}
