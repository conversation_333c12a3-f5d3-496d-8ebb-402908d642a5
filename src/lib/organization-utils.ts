import { db } from '@/lib/db'
import { schema } from '@/db/schema'
import { eq } from 'drizzle-orm'

export async function setActiveOrganizationForUser(userId: string) {
  try {
    // Find the user's organization
    const member = await db
      .select({
        organizationId: schema.member.organizationId,
        organizationName: schema.organization.name,
      })
      .from(schema.member)
      .innerJoin(
        schema.organization,
        eq(schema.member.organizationId, schema.organization.id)
      )
      .where(eq(schema.member.userId, userId))
      .limit(1)

    if (member.length > 0) {
      // Update the user's session to set the active organization
      await db
        .update(schema.session)
        .set({ activeOrganizationId: member[0].organizationId })
        .where(eq(schema.session.userId, userId))

      console.log(
        `Set active organization "${member[0].organizationName}" for user ${userId}`
      )
      return member[0].organizationId
    }

    return null
  } catch (error) {
    console.error('Failed to set active organization:', error)
    return null
  }
}

export async function getActiveOrganizationForUser(userId: string) {
  try {
    const member = await db
      .select({
        organizationId: schema.member.organizationId,
        organizationName: schema.organization.name,
        organizationSlug: schema.organization.slug,
        organizationLogo: schema.organization.logo,
        role: schema.member.role,
      })
      .from(schema.member)
      .innerJoin(
        schema.organization,
        eq(schema.member.organizationId, schema.organization.id)
      )
      .where(eq(schema.member.userId, userId))
      .limit(1)

    return member.length > 0 ? member[0] : null
  } catch (error) {
    console.error('Failed to get active organization:', error)
    return null
  }
}
