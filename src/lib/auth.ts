import { betterAuth } from 'better-auth'
import { drizzleAdapter } from 'better-auth/adapters/drizzle'
import { nextCookies } from 'better-auth/next-js'
import { organization } from 'better-auth/plugins'
import { emailHarmony } from 'better-auth-harmony'
import { stripe } from '@better-auth/stripe'
import Stripe from 'stripe'
import { eq } from 'drizzle-orm'

// import ForgotPasswordEmail from '@/components/emails/forgot-password'

import { db } from '@/lib/db'
import { schema } from '@/db/schema'
import {
  sendEmail,
  createVerificationEmailTemplate,
  createPasswordResetEmailTemplate,
  createWelcomeEmailTemplate,
  createOrganizationInvitationTemplate,
  createSubscriptionWelcomeEmailTemplate,
} from '@/lib/email'
import { getActiveOrganizationForUser } from '@/lib/organization-utils'
import { plans } from '@/config/constants'

const stripeClient = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-07-30.basil',
})

export const auth = betterAuth({
  emailVerification: {
    autoSignInAfterVerification: true,
    sendVerificationEmail: async ({ user, url }) => {
      const { html, text } = createVerificationEmailTemplate(url, user.name)
      await sendEmail({
        to: user.email,
        subject: 'Verify your email address - Adori AI',
        text,
        html,
      })
    },
    afterEmailVerification: async user => {
      console.log(`Email verified for user: ${user.email}`)
      console.log(
        'Organization will be created automatically when user logs in'
      )

      // Send welcome email after successful email verification
      try {
        const { html, text } = createWelcomeEmailTemplate(user.name)
        await sendEmail({
          to: user.email,
          subject: 'Welcome to Adori AI! 🎉',
          text,
          html,
        })
        console.log(`Welcome email sent to: ${user.email}`)
      } catch (error) {
        console.error('Failed to send welcome email:', error)
        // Don't throw error to avoid breaking the verification flow
      }
    },
  },

  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
    sendResetPassword: async ({ user, url }) => {
      const { html, text } = createPasswordResetEmailTemplate(url, user.name)
      await sendEmail({
        to: user.email,
        subject: 'Reset your password - Adori AI',
        text,
        html,
      })
    },
    onPasswordReset: async ({ user }) => {
      // Log password reset for security monitoring
      console.log(`Password reset completed for user: ${user.email}`)
    },
  },
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_OAUTH_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_OAUTH_CLIENT_SECRET as string,
    },
  },
  database: drizzleAdapter(db, {
    provider: 'pg',
    schema: schema,
  }),
  databaseHooks: {
    user: {
      create: {
        before: async user => {
          // Debug: Log the user being created
          console.log('Creating user with email:', user.email)
          console.log('User data:', user)
        },
        after: async user => {
          // Debug: Log the created user
          console.log('Created user:', user)
          console.log(
            'User created successfully - organization will be created after email verification'
          )
        },
      },
    },
    session: {
      create: {
        before: async session => {
          try {
            const organization = await getActiveOrganizationForUser(
              session.userId
            )
            console.log('Session creation - found organization:', {
              userId: session.userId,
              organizationId: organization?.organizationId || null,
              organizationName: organization?.organizationName || null,
            })

            return {
              data: {
                ...session,
                activeOrganizationId: organization?.organizationId || null,
              },
            }
          } catch (error) {
            console.error('Error in session creation hook:', error)
            return {
              data: {
                ...session,
                activeOrganizationId: null,
              },
            }
          }
        },
      },
    },
  },
  plugins: [
    stripe({
      stripeClient,
      stripeWebhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
      createCustomerOnSignUp: true,
      getCustomerCreateParams: async ({ user }) => {
        // Customize the Stripe customer creation parameters
        const organization = await getActiveOrganizationForUser(user.id)
        return {
          metadata: {
            email: user.email,
            userId: user.id,
            organizationId: organization?.organizationId || null,
          },
        }
      },
      onEvent: async event => {
        // Handle any Stripe event
        console.log(`Stripe webhook event: ${event.type}`)

        switch (event.type) {
          case 'invoice.payment_succeeded':
            console.log('Invoice payment succeeded:', event.data.object)

            // Handle subscription renewal
            const invoice = event.data.object as Stripe.Invoice
            if (
              invoice.billing_reason === 'subscription_cycle' &&
              typeof (invoice as { subscription?: string }).subscription ===
                'string'
            ) {
              const subscriptionId = (invoice as { subscription?: string })
                .subscription!
              console.log('Subscription renewal detected:', subscriptionId)

              try {
                // Get subscription details from Stripe
                const subscription =
                  await stripeClient.subscriptions.retrieve(subscriptionId)

                // Get the plan details from the subscription metadata or items
                const subscriptionData = subscription as Stripe.Subscription
                const planName =
                  subscriptionData.items?.data?.[0]?.price?.lookup_key ||
                  subscriptionData.items?.data?.[0]?.price?.id
                const plan = plans.find(
                  p => p.priceId === planName || p.id.toString() === planName
                )

                if (plan && subscriptionData.metadata?.organizationId) {
                  // Import the reset function
                  const { resetUsageForSubscription } = await import(
                    '@/lib/usage-utils'
                  )

                  // Reset usage for the organization
                  // Use billing_cycle_anchor + billing period for end date
                  const result = await resetUsageForSubscription(
                    subscriptionData.metadata.organizationId,
                    plan.name,
                    new Date(
                      (subscriptionData.billing_cycle_anchor +
                        30 * 24 * 60 * 60) *
                        1000
                    ) // 30 days from billing cycle anchor
                  )

                  if (result.success) {
                    console.log(
                      `Usage reset successfully for subscription renewal: ${subscriptionData.id}`
                    )
                  } else {
                    console.error(
                      'Failed to reset usage for subscription renewal:',
                      result.error
                    )
                  }
                }
              } catch (error) {
                console.error(
                  'Error handling subscription renewal webhook:',
                  error
                )
              }
            }
            break

          case 'invoice.paid':
            console.log('Invoice paid:', event.data.object)
            break

          case 'payment_intent.succeeded':
            console.log('Payment succeeded:', event.data.object)
            break

          case 'customer.subscription.trial_will_end':
            console.log('Trial ending soon:', event.data.object)
            break
        }
      },
      subscription: {
        enabled: true,
        plans: plans,
        authorizeReference: async ({ user, referenceId }) => {
          // If referenceId is 'user', allow it (user-level subscription)
          if (referenceId === 'user') {
            return true
          }

          // For organization-level subscriptions, check if user is a member
          try {
            const member = await db
              .select()
              .from(schema.member)
              .where(
                eq(schema.member.userId, user.id) &&
                  eq(schema.member.organizationId, referenceId)
              )
              .limit(1)

            if (member.length === 0) {
              return false
            }

            // Allow if user is owner or admin of the organization
            return member[0].role === 'owner' || member[0].role === 'admin'
          } catch (error) {
            console.error('Error checking organization membership:', error)
            return false
          }
        },
        getCheckoutSessionParams: async ({ user }) => {
          // Get the active organization for the user
          const activeOrg = await getActiveOrganizationForUser(user.id)

          return {
            params: {
              allow_promotion_codes: true,
              metadata: {
                name: user.name || '',
                email: user.email,
                organizationId: activeOrg?.organizationId || user.id,
                userId: user.id,
              },
            },
          }
        },
        onSubscriptionComplete: async ({
          subscription,
          plan,
          stripeSubscription,
        }) => {
          // Called when a subscription is successfully created
          console.log(
            `Subscription completed: ${subscription.id} for plan ${plan.name}`
          )
          console.log(`Reference ID: ${subscription.referenceId}`)
          console.log(`Metadata: ${stripeSubscription.metadata}`)

          try {
            // Import the reset function
            const { resetUsageForSubscription } = await import(
              '@/lib/usage-utils'
            )

            const organizationId = stripeSubscription.metadata?.organizationId

            if (organizationId) {
              // Reset usage for the organization using referenceId
              const result = await resetUsageForSubscription(
                organizationId,
                plan.name,
                subscription.periodEnd
              )

              if (result.success) {
                console.log(
                  `Usage reset successfully for subscription ${subscription.id}`
                )

                // Send subscription welcome email
                try {
                  const { html, text } = createSubscriptionWelcomeEmailTemplate(
                    {
                      userName: stripeSubscription.metadata?.name || '',
                      planName: plan.name,
                      planLimits: (plan.limits as {
                        projects: number
                        videoExports: number
                        aiImages: number
                        storage: number
                        teamMembers: number
                      }) || {
                        projects: 0,
                        videoExports: 0,
                        aiImages: 0,
                        storage: 0,
                        teamMembers: 0,
                      },
                    }
                  )

                  await sendEmail({
                    to: stripeSubscription.metadata?.email || '',
                    subject: `Welcome to ${plan.name} Plan! 🎉 - Adori AI`,
                    text,
                    html,
                  })

                  console.log(
                    `Subscription welcome email sent to ${stripeSubscription.metadata?.email || ''}`
                  )
                } catch (emailError) {
                  console.error(
                    'Failed to send subscription welcome email:',
                    emailError
                  )
                  // Don't throw error to avoid breaking the subscription flow
                }
              } else {
                console.error('Failed to reset usage:', result.error)
              }
            } else {
              console.error(
                'Could not determine organization ID for subscription reset'
              )
            }
          } catch (error) {
            console.error('Error in subscription completion handler:', error)
          }
        },
        onSubscriptionUpdate: async ({ subscription }) => {
          // Called when a subscription is updated
          console.log(`Subscription updated: ${subscription.id}`)
          console.log(`New status: ${subscription.status}`)

          // Add usage reset for plan upgrades
          if (subscription.status === 'active') {
            try {
              const { resetUsageForSubscription } = await import(
                '@/lib/usage-utils'
              )

              const referenceId = subscription.referenceId

              if (referenceId) {
                await resetUsageForSubscription(
                  referenceId,
                  subscription.plan,
                  subscription.periodEnd
                )
              }
            } catch (error) {
              console.error(
                'Error resetting usage for subscription update:',
                error
              )
            }
          }
        },
        onSubscriptionCancel: async ({ subscription, cancellationDetails }) => {
          // Called when a subscription is canceled
          console.log(`Subscription canceled: ${subscription.id}`)
          console.log(`Cancellation details:`, cancellationDetails)

          // You can add custom logic here like:
          // - Send cancellation email
          // - Update user/organization limits
          // - Log subscription cancellation
        },
        onSubscriptionDeleted: async ({ subscription }) => {
          // Called when a subscription is deleted
          console.log(`Subscription deleted: ${subscription.id}`)
        },
      },
    }),
    emailHarmony({
      allowNormalizedSignin: true,
    }),
    organization({
      sendInvitationEmail: async data => {
        const inviteLink = `${process.env.NEXT_PUBLIC_APP_URL}/accept-invitation/${data.id}`
        const { html, text } = createOrganizationInvitationTemplate({
          email: data.email,
          invitedByUsername: data.inviter.user.name || data.inviter.user.email,
          invitedByEmail: data.inviter.user.email,
          teamName: data.organization.name,
          inviteLink,
        })
        await sendEmail({
          to: data.email,
          subject: `You've been invited to join ${data.organization.name} on Adori AI`,
          text,
          html,
        })
      },
      organizationCreation: {
        // beforeCreate: async ({ organization, user }) => {
        //   // Use our utility functions for consistent naming and slug generation
        //   const orgName = getDisplayName(user.name || '', user.email)
        //   const orgSlug = generateOrganizationSlug(user.name || '', user.email)
        //   const orgLogo = getOrganizationLogo(user.name || '', user.email)
        //   return {
        //     data: {
        //       ...organization,
        //       name: orgName,
        //       slug: orgSlug,
        //       logo: orgLogo,
        //     },
        //   }
        // },
        // afterCreate: async ({ organization, user }) => {
        //   console.log(
        //     `Organization "${organization.name}" created for user ${user.email}`
        //   )
        // },
      },
    }),
    nextCookies(), //Should be last
  ],
})
