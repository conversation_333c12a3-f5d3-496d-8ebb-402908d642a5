export function createOrganizationInvitationTemplate({
  email,
  invitedByUsername,
  invitedByEmail,
  teamName,
  inviteLink,
}: {
  email: string
  invitedByUsername: string
  invitedByEmail: string
  teamName: string
  inviteLink: string
}) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>You've been invited to join ${teamName} - <PERSON>ori AI</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #FF8C00 0%, #FF1493 50%, #8A2BE2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background-color: #8A2BE2; color: white !important; padding: 14px 19px; text-decoration: none; border-radius: 8px; margin: 20px 0; text-align: center; box-sizing: border-box; }
        .button:link, .button:visited, .button:hover, .button:active { color: white !important; text-decoration: none; }
        .ii .button, .ii .button:link, .ii .button:visited, .ii .button:hover, .ii .button:active { color: white !important; }
        a.button, a.button:link, a.button:visited, a.button:hover, a.button:active { color: white !important; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .highlight { background: #e8f4fd; border-left: 4px solid #FF8C00; padding: 15px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>You're Invited!</h1>
        </div>
        <div class="content">
          <h2>Hi there,</h2>
          <p><strong>${invitedByUsername}</strong> (${invitedByEmail}) has invited you to join <strong>${teamName}</strong> on Adori AI!</p>

          <div class="highlight">
            <p><strong>What is Adori AI?</strong></p>
            <p>Adori AI is a powerful platform that transforms your content into engaging videos using artificial intelligence. Create professional videos from text, blogs, PDFs, and podcasts in minutes!</p>
          </div>

          <p>As a member of ${teamName}, you'll be able to:</p>
          <ul>
            <li>Create and collaborate on video projects</li>
            <li>Access shared resources and templates</li>
            <li>Work with your team on content creation</li>
            <li>Manage projects and workflows together</li>
          </ul>

          <div style="text-align: center; color: #fff;">
            <a href="${inviteLink}" class="button">Accept Invitation</a>
          </div>

          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #FF8C00;">${inviteLink}</p>

          <p><strong>Note:</strong> This invitation link will expire in 48 hours for security reasons.</p>
        </div>
        <div class="footer">
          <p>&copy; 2025 Adori AI. All rights reserved.</p>
          <p>This invitation was sent to ${email} by ${invitedByEmail}.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
You've been invited to join ${teamName} - Adori AI

Hi there,

${invitedByUsername} (${invitedByEmail}) has invited you to join ${teamName} on Adori AI!

What is Adori AI?
Adori AI is a powerful platform that transforms your content into engaging videos using artificial intelligence. Create professional videos from text, blogs, PDFs, and podcasts in minutes!

As a member of ${teamName}, you'll be able to:
• Create and collaborate on video projects
• Access shared resources and templates
• Work with your team on content creation
• Manage projects and workflows together

Accept your invitation here: ${inviteLink}

Note: This invitation link will expire in 48 hours for security reasons.

Best regards,
The Adori AI Team

© 2025 Adori AI. All rights reserved.
This invitation was sent to ${email} by ${invitedByEmail}.
  `

  return { html, text }
}
