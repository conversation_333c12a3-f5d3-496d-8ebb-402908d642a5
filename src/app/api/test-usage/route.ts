import { NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { usage } from '@/db/schema'
import { eq } from 'drizzle-orm'
import { getUserSession } from '@/lib/user-utils'
import { getUIPlans } from '@/lib/plan-utils'

export async function GET() {
  try {
    const session = await getUserSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const activeOrganizationId = session?.session?.activeOrganizationId

    // Use active organization ID if available, otherwise use user ID
    const referenceId = activeOrganizationId || userId

    // Get current usage record
    const usageRecord = await db
      .select()
      .from(usage)
      .where(eq(usage.organizationId, referenceId))
      .limit(1)

    if (!usageRecord || usageRecord.length === 0) {
      return NextResponse.json({
        error: 'No usage record found',
        referenceId,
        userId,
        activeOrganizationId,
      })
    }

    const currentUsage = usageRecord[0]

    // Debug information
    const debugInfo = {
      referenceId,
      userId,
      activeOrganizationId,
      databasePlanType: currentUsage.planType,
      availablePlans: getUIPlans().map(p => ({
        name: p.name,
        videoCreation: p.videoCreation,
        videoDownloads: p.videoDownloads,
      })),
      currentUsage: {
        projectsUsed: currentUsage.projectsUsed,
        videoExportsUsed: currentUsage.videoExportsUsed,
        aiImagesUsed: currentUsage.aiImagesUsed,
        teamMembersUsed: currentUsage.teamMembersUsed,
        storageUsed: currentUsage.storageUsed,
        planType: currentUsage.planType,
        planStatus: currentUsage.planStatus,
      },
    }

    return NextResponse.json(debugInfo)
  } catch (error) {
    console.error('Test usage API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Temporary endpoint to fix plan type
export async function POST() {
  try {
    const session = await getUserSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id
    const activeOrganizationId = session?.session?.activeOrganizationId
    const referenceId = activeOrganizationId || userId

    // Update the plan type to 'basic' (lowercase)
    await db
      .update(usage)
      .set({
        planType: 'basic',
        updatedAt: new Date(),
      })
      .where(eq(usage.organizationId, referenceId))

    return NextResponse.json({
      success: true,
      message: 'Plan type updated to basic',
      referenceId,
    })
  } catch (error) {
    console.error('Error updating plan type:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
