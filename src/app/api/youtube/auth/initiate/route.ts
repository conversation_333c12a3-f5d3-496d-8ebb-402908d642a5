import { NextResponse } from 'next/server'
// import { auth } from '@clerk/nextjs/server'
import { getUserSession } from '@/lib/user-utils'
import { generateAuthUrl } from '@/lib/youtube-auth'
import { withPerformanceMonitoring } from '@/lib/performance'
import crypto from 'crypto'

async function handler() {
  try {
    // Check if user is authenticated
    // const { userId } = await auth()
    const session = await getUserSession()
    const userId = session?.user?.id
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Generate a secure state parameter for CSRF protection
    const state = crypto.randomBytes(32).toString('hex')

    // Store state in a way that can be verified later
    // For now, we'll include the userId in the state and verify it in callback
    const stateData = {
      userId,
      timestamp: Date.now(),
      random: state,
    }

    // Encode state data
    const encodedState = Buffer.from(JSON.stringify(stateData)).toString(
      'base64'
    )

    // Generate the authorization URL
    const authUrl = generateAuthUrl(encodedState)

    // Return the authorization URL
    return NextResponse.json({ authUrl })
  } catch (error) {
    console.error('Error initiating YouTube OAuth:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

export const GET = withPerformanceMonitoring(
  handler,
  '/api/youtube/auth/initiate'
)
