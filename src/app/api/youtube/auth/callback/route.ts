import { NextRequest, NextResponse } from 'next/server'
import { exchangeCodeForTokens, getChannelInfo } from '@/lib/youtube-auth'
import { encryptTokens } from '@/lib/encryption'
import { db } from '@/lib/db'
import { youtubeConnections } from '@/db/schema'
import { eq, and } from 'drizzle-orm'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const code = searchParams.get('code')
  const state = searchParams.get('state')
  const error = searchParams.get('error')

  // Handle OAuth errors
  if (error) {
    return new NextResponse(
      `
      <!DOCTYPE html>
      <html>
        <head>
          <title>YouTube Connection Failed</title>
        </head>
        <body>
          <script>
            window.opener?.postMessage({
              type: 'YOUTUBE_AUTH_ERROR',
              error: '${error}'
            }, '*');
            window.close();
          </script>
        </body>
      </html>
      `,
      { headers: { 'Content-Type': 'text/html' } }
    )
  }

  if (!code || !state) {
    return new NextResponse(
      `
      <!DOCTYPE html>
      <html>
        <head>
          <title>YouTube Connection Failed</title>
        </head>
        <body>
          <script>
            window.opener?.postMessage({
              type: 'YOUTUBE_AUTH_ERROR',
              error: 'Missing authorization code or state'
            }, '*');
            window.close();
          </script>
        </body>
      </html>
      `,
      { headers: { 'Content-Type': 'text/html' } }
    )
  }

  try {
    // Decode and verify state
    const stateData = JSON.parse(Buffer.from(state, 'base64').toString())
    const { userId, timestamp } = stateData

    // Check if state is not too old (5 minutes max)
    if (Date.now() - timestamp > 5 * 60 * 1000) {
      throw new Error('State parameter expired')
    }

    // Exchange code for tokens
    const tokenData = await exchangeCodeForTokens(code)

    // Get channel information
    const channelInfo = await getChannelInfo(tokenData.access_token)

    // Encrypt tokens for storage
    const encryptedTokens = encryptTokens({
      access_token: tokenData.access_token,
      refresh_token: tokenData.refresh_token,
    })

    // Check if this channel is already connected for this user
    const existingConnection = await db
      .select()
      .from(youtubeConnections)
      .where(
        and(
          eq(youtubeConnections.userId, userId),
          eq(youtubeConnections.channelId, channelInfo.id)
        )
      )
      .limit(1)

    if (existingConnection.length > 0) {
      // Update existing connection
      await db
        .update(youtubeConnections)
        .set({
          accessToken: encryptedTokens.encryptedAccessToken,
          refreshToken: encryptedTokens.encryptedRefreshToken,
          expiresAt: tokenData.expires_at || new Date(Date.now() + 3600 * 1000),
          channelTitle: channelInfo.title,
          channelThumbnailUrl: channelInfo.thumbnailUrl,
          channelDescription: channelInfo.description,
          scopes: tokenData.scope?.split(' ') || [],
          updatedAt: new Date(),
          isActive: true,
        })
        .where(eq(youtubeConnections.id, existingConnection[0].id))
    } else {
      // Create new connection
      await db.insert(youtubeConnections).values({
        userId,
        accessToken: encryptedTokens.encryptedAccessToken,
        refreshToken: encryptedTokens.encryptedRefreshToken,
        expiresAt: tokenData.expires_at || new Date(Date.now() + 3600 * 1000), // Default 1 hour
        channelId: channelInfo.id,
        channelTitle: channelInfo.title,
        channelThumbnailUrl: channelInfo.thumbnailUrl,
        channelDescription: channelInfo.description,
        scopes: tokenData.scope?.split(' ') || [],
      })
    }

    // Return success page that notifies parent window
    return new NextResponse(
      `
      <!DOCTYPE html>
      <html>
        <head>
          <title>YouTube Connected Successfully</title>
        </head>
        <body>
          <div style="text-align: center; padding: 20px; font-family: Arial, sans-serif;">
            <h2>YouTube Connected Successfully!</h2>
            <p>You can close this window.</p>
          </div>
          <script>
            console.log('Sending success message to parent window');
            try {
              if (window.opener) {
                window.opener.postMessage({
                  type: 'YOUTUBE_AUTH_SUCCESS',
                  channelInfo: ${JSON.stringify(channelInfo)}
                }, window.location.origin);
                console.log('Success message sent');
              } else {
                console.error('No opener window found');
              }
            } catch (error) {
              console.error('Error sending message:', error);
            }
            setTimeout(() => window.close(), 1000);
          </script>
        </body>
      </html>
      `,
      { headers: { 'Content-Type': 'text/html' } }
    )
  } catch (error) {
    console.error('Error in YouTube OAuth callback:', error)

    return new NextResponse(
      `
      <!DOCTYPE html>
      <html>
        <head>
          <title>YouTube Connection Failed</title>
        </head>
        <body>
          <div style="text-align: center; padding: 20px; font-family: Arial, sans-serif;">
            <h2>❌ Connection Failed</h2>
            <p>There was an error connecting your YouTube account.</p>
          </div>
          <script>
            console.log('Sending error message to parent window');
            try {
              if (window.opener) {
                window.opener.postMessage({
                  type: 'YOUTUBE_AUTH_ERROR',
                  error: 'Failed to complete YouTube connection'
                }, window.location.origin);
                console.log('Error message sent');
              } else {
                console.error('No opener window found');
              }
            } catch (error) {
              console.error('Error sending message:', error);
            }
            setTimeout(() => window.close(), 2000);
          </script>
        </body>
      </html>
      `,
      { headers: { 'Content-Type': 'text/html' } }
    )
  }
}
