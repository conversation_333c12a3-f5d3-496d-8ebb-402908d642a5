'use client'

import { useState, FormEvent, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { LoaderDialog } from '@/components/ui/loader-dialog'
import { AlertCircle } from 'lucide-react'
import { toast } from '@/lib/toast'
import { useVideoStore } from '@/store/video-store'
import type { ElevenVoice } from '@/hooks/useElevenVoicesQuery'
import type { AutomationRequest } from '@/lib/types'
import { useInngestRunStatus } from '@/lib/useInngestRunStatus'
import { useQueryClient } from '@tanstack/react-query'
import { useDefaultAutopickValue } from '@/hooks/use-gated-autopick-options'
// import { useUser } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'
// Import reusable components
import {
  ToneSelector,
  AudienceSelector,
  PlatformSelector,
  IncludeOptions,
  KeywordsInput,
  AdvancedSettings,
  GatedActionButton,
  VideoFormSkeleton,
  type BlogVideoConfig,
} from '@/app/(dashboard)/_components/video-form'

// URL validation utility
function isValidUrl(string: string): boolean {
  try {
    const url = new URL(string)
    return url.protocol === 'http:' || url.protocol === 'https:'
  } catch {
    return false
  }
}

export function BlogToVideoForm() {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()
  const router = useRouter()
  const queryClient = useQueryClient()

  const [isInitialLoading, setIsInitialLoading] = useState(true)
  const { defaultValue: defaultAutopick } = useDefaultAutopickValue()

  // Loading state tracking
  const [progressMessage, setProgressMessage] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [urlError, setUrlError] = useState('')

  const [config, setConfig] = useState<BlogVideoConfig>({
    blogUrl: '',
    tone: 'friendly',
    audience: 'general',
    platform: 'youtube',
    duration: 60,
    includeHook: true,
    includeCTA: true,
    language: 'english',
    orientation: 'landscape',
    autopick: defaultAutopick,
    voice: null,
    keywords: '',
  })

  const [eventId, setEventId] = useState<string | null>(null)
  const { status, output } = useInngestRunStatus(eventId)

  // Simulate initial loading for better UX
  useEffect(() => {
    // Show skeleton for a short time to prevent flash of content
    const timer = setTimeout(() => {
      setIsInitialLoading(false)
    }, 100) // Just enough time to prevent layout shift

    return () => clearTimeout(timer)
  }, [])

  const updateConfig = (
    key: keyof BlogVideoConfig,
    value: string | number | boolean | ElevenVoice | null
  ) => {
    setConfig(prev => ({ ...prev, [key]: value }))

    // Clear URL error when user starts typing
    if (key === 'blogUrl' && urlError) {
      setUrlError('')
    }
  }

  const validateBlogUrl = (url: string): boolean => {
    if (!url.trim()) {
      setUrlError('Blog URL is required')
      return false
    }

    if (!isValidUrl(url)) {
      setUrlError(
        'Please enter a valid URL (e.g., https://example.com/blog-post)'
      )
      return false
    }

    setUrlError('')
    return true
  }

  const handleVoiceSelect = (voice: ElevenVoice) => {
    updateConfig('voice', voice)
    toast.success(`Voice "${voice.name}" selected`)
  }

  const simulateProgress = () => {
    setProgressMessage('🌐 Fetching and analyzing blog content...')

    setTimeout(() => {
      setProgressMessage('🤖 Converting blog content to video script...')
    }, 2000)

    setTimeout(() => {
      setProgressMessage('🎬 Finding perfect visual assets for your content...')
    }, 4000)

    setTimeout(() => {
      setProgressMessage('🎙️ Converting script to professional voiceovers...')
    }, 6000)

    setTimeout(() => {
      setProgressMessage('✨ Finalizing scenes and preparing your video...')
    }, 8000)

    setTimeout(() => {
      setProgressMessage('🎉 Almost done! Wrapping everything up...')
    }, 10000)
  }

  const handleGenerateVideo = async (e?: FormEvent) => {
    e?.preventDefault()

    // Validate blog URL
    if (!validateBlogUrl(config.blogUrl)) {
      return
    }

    if (!config.tone || !config.audience || !config.platform) {
      toast.error('Please select tone, audience, and platform')
      return
    }

    if (!config.voice) {
      toast.error('Please select a voice for the video')
      return
    }

    setIsGenerating(true)
    simulateProgress()

    const userId = session?.user?.id
    try {
      // Prepare automation request with blog URL
      const automationRequest: AutomationRequest = {
        idea: '',
        blogUrl: config.blogUrl,
        tone: config.tone,
        audience: config.audience,
        platform: config.platform,
        hook: config.includeHook,
        callToAction: config.includeCTA,
        keywords: config.keywords || undefined,
        duration: config.duration,
        language: config.language,
        orientation: config.orientation,
        autopick: config.autopick,
        voice: config.voice,
        userId: userId,
        organizationId: session?.session?.activeOrganizationId || undefined,
        method: 'Blog to Video',
      }

      // Call the new inngest API route
      const response = await fetch('/api/generate-video-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(automationRequest),
      })
      if (!response.ok) throw new Error('Failed to generate video')
      const result = await response.json()
      setEventId(result.eventId)
    } catch (error) {
      console.error('Video generation error:', error)
      setProgressMessage('')
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to generate video. Please try again.'
      )
      setIsGenerating(false)
    }
  }

  // Watch for polling completion
  useEffect(() => {
    if (
      status === 'Completed' &&
      output &&
      typeof output === 'object' &&
      output !== null &&
      'projectId' in output &&
      typeof output.projectId === 'string'
    ) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const project = output as any // API response will be transformed by setProjectData
      setProgressMessage('🎉 Video generation complete! Redirecting...')
      useVideoStore.getState().setProjectData(project)
      // Invalidate projects cache when a new project is created via Inngest
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      toast.success('Video created successfully!')
      setTimeout(() => {
        router.push(`/scene-editor?projectId=${project.projectId}`)
      }, 1500)
      setIsGenerating(false)
    } else if (status === 'Failed' || status === 'Cancelled') {
      setProgressMessage('')
      toast.error('Video generation failed. Please try again.')
      setIsGenerating(false)
    }
  }, [status, output, queryClient, router])

  // Show skeleton during initial loading
  if (isInitialLoading) {
    return <VideoFormSkeleton showUrlField />
  }

  return (
    <>
      <div className='space-y-4 animate-in fade-in-50 duration-300'>
        {/* Blog URL */}
        <div className='space-y-1'>
          <Label htmlFor='blogUrl' className='text-sm'>
            Blog URL <span className='text-red-500'>*</span>
          </Label>
          <Input
            id='blogUrl'
            type='url'
            placeholder='https://yourblog.com/amazing-article'
            value={config.blogUrl}
            onChange={e => updateConfig('blogUrl', e.target.value)}
            className={`h-9 text-sm ${urlError ? 'border-red-500' : ''}`}
            disabled={isGenerating}
          />
          {urlError && (
            <div className='flex items-center gap-1 text-red-500 text-xs'>
              <AlertCircle className='h-3 w-3' />
              {urlError}
            </div>
          )}
          <p className='text-xs text-muted-foreground'>
            Enter the URL of the blog post you want to convert
          </p>
        </div>

        {/* Dropdowns Row */}
        <div className='flex gap-x-4 gap-y-2 flex-wrap'>
          <ToneSelector
            value={config.tone}
            onValueChange={value => updateConfig('tone', value)}
            disabled={isGenerating}
          />

          <AudienceSelector
            value={config.audience}
            onValueChange={value => updateConfig('audience', value)}
            disabled={isGenerating}
          />

          <PlatformSelector
            value={config.platform}
            onValueChange={value => updateConfig('platform', value)}
            disabled={isGenerating}
          />
        </div>

        {/* Include Options */}
        <IncludeOptions
          includeHook={config.includeHook}
          includeCTA={config.includeCTA}
          onIncludeHookChange={checked => updateConfig('includeHook', checked)}
          onIncludeCTAChange={checked => updateConfig('includeCTA', checked)}
          disabled={isGenerating}
        />

        {/* Keywords */}
        <KeywordsInput
          value={config.keywords}
          onChange={value => updateConfig('keywords', value)}
          placeholder='e.g. technology, innovation, startup'
          disabled={isGenerating}
        />

        {/* Advanced Settings */}
        <AdvancedSettings
          duration={config.duration}
          onDurationChange={value => updateConfig('duration', value)}
          voice={config.voice}
          onVoiceSelect={handleVoiceSelect}
          language={config.language}
          onLanguageChange={value => updateConfig('language', value)}
          orientation={config.orientation}
          onOrientationChange={value => updateConfig('orientation', value)}
          autopick={config.autopick}
          onAutopickChange={value => updateConfig('autopick', value)}
          disabled={isGenerating}
        />
      </div>

      {/* Action Button */}
      <GatedActionButton
        onClick={handleGenerateVideo}
        disabled={!config.blogUrl.trim() || !config.voice || !!urlError}
        isGenerating={isGenerating}
        progressMessage={progressMessage}
        actionText='Generate Video'
        loadingText='Converting Blog...'
      />

      {/* Loader Dialog */}
      <LoaderDialog
        open={isGenerating || status === 'Running'}
        title='AI agent is cooking your video'
        subtitle={
          status === 'Running'
            ? 'Converting your blog content. This may take a few minutes...'
            : progressMessage || 'This may take a few minutes...'
        }
      />
    </>
  )
}
