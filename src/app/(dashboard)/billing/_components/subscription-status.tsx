'use client'
import React from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  useSubscription,
  useCachedActiveSubscription,
} from '@/hooks/useSubscription'
import { toast } from 'sonner'
import { Calendar, RefreshCw, CreditCard } from 'lucide-react'
import { getBillingPeriod } from '@/lib/subscription-utils'

export function SubscriptionStatus() {
  const { subscription, isLoading, error, refetch } =
    useCachedActiveSubscription()
  const { cancelSubscription, restoreSubscription } = useSubscription()

  const handleCancel = async () => {
    if (!subscription) {
      toast.error('No active subscription to cancel')
      return
    }

    const { error } = await cancelSubscription({
      subscriptionId: subscription.id,
      returnUrl: '/billing',
    })

    if (error) {
      toast.error('Failed to cancel subscription')
    } else {
      // Refetch subscription data to update the UI
      await refetch()
    }
  }

  const handleRestore = async () => {
    if (!subscription) {
      toast.error('No subscription to restore')
      return
    }

    const { error } = await restoreSubscription({
      subscriptionId: subscription.id,
    })

    if (error) {
      toast.error('Failed to restore subscription')
    } else {
      // Refetch subscription data to update the UI
      await refetch()
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex items-center justify-center py-4'>
            <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-primary'></div>
            <span className='ml-2 text-sm text-muted-foreground'>
              Loading...
            </span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex items-center justify-center py-4'>
            <div className='text-center'>
              <p className='text-sm text-destructive mb-2'>
                Failed to load subscription
              </p>
              <p className='text-xs text-muted-foreground'>{error.message}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Subscription Status</CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='flex items-center justify-between'>
          <div>
            <p className='text-sm font-medium'>Current Plan</p>
            <p className='text-2xl font-bold capitalize'>
              {subscription?.plan || 'Free'}
            </p>
          </div>
          <Badge
            variant={
              subscription?.status === 'active' ? 'default' : 'secondary'
            }
          >
            {subscription?.status || 'No Subscription'}
          </Badge>
        </div>

        {subscription && (
          <div className='space-y-3'>
            <div className='flex items-center gap-3'>
              <Calendar className='h-4 w-4 text-muted-foreground' />
              <div className='flex-1'>
                <p className='text-sm text-muted-foreground'>
                  Plan start date:{' '}
                  <span className='text-foreground font-medium'>
                    {subscription.periodStart
                      ? new Date(subscription.periodStart).toLocaleDateString()
                      : 'N/A'}
                  </span>
                </p>
              </div>
            </div>

            <Separator />

            <div className='flex items-center gap-3'>
              <RefreshCw className='h-4 w-4 text-muted-foreground' />
              <div className='flex-1'>
                <p className='text-sm text-muted-foreground'>
                  Auto-renew:{' '}
                  <span className='text-foreground font-medium'>
                    {subscription.cancelAtPeriodEnd ? 'No' : 'Yes'}
                  </span>
                </p>
              </div>
            </div>

            {subscription.periodEnd && (
              <>
                <Separator />
                <div className='flex items-center gap-3'>
                  <CreditCard className='h-4 w-4 text-muted-foreground' />
                  <div className='flex-1'>
                    <p className='text-sm text-muted-foreground'>
                      Renewal date:{' '}
                      <span className='text-foreground font-medium'>
                        {new Date(subscription.periodEnd).toLocaleDateString()}
                      </span>
                    </p>
                  </div>
                </div>
              </>
            )}

            <Separator />

            <div className='flex items-center gap-3'>
              <Calendar className='h-4 w-4 text-muted-foreground' />
              <div className='flex-1'>
                <p className='text-sm text-muted-foreground'>
                  Billing period:{' '}
                  <span className='text-foreground font-medium capitalize'>
                    {getBillingPeriod(
                      subscription.periodStart,
                      subscription.periodEnd
                    )}
                  </span>
                </p>
              </div>
            </div>
          </div>
        )}

        <Separator className='my-4' />

        <div className='flex gap-2'>
          {subscription && subscription.status === 'active' && (
            <>
              {subscription.cancelAtPeriodEnd ? (
                <Button onClick={handleRestore} variant='default' size='sm'>
                  Don&apos;t Cancel Subscription
                </Button>
              ) : (
                <Button onClick={handleCancel} variant='destructive' size='sm'>
                  Cancel Subscription
                </Button>
              )}
            </>
          )}
        </div>

        {/* Cancellation notice */}
        {subscription &&
          subscription.cancelAtPeriodEnd &&
          subscription.periodEnd && (
            <div className='mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md'>
              <p className='text-sm text-destructive font-medium'>
                Your subscription will be cancelled on:{' '}
                {new Date(subscription.periodEnd).toLocaleDateString()}
              </p>
            </div>
          )}
      </CardContent>
    </Card>
  )
}
