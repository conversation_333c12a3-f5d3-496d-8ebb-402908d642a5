'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Mic, RotateCw, Play, Pause } from 'lucide-react'
import { useGatedVoiceGeneration } from '@/hooks/use-gated-voice-generation'
import { useDebounceSceneUpdate } from '@/hooks/use-debounced-scene-updates'
import { Scene } from '@/types/video'
import { toast } from '@/lib/toast'
// import { useUser } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'
// import { clearCaptions } from '../remotion/subtitles/subtitle-system'

interface VoiceoverSectionProps {
  scene: Scene
  sceneIndex: number
  onOpenVoicePicker: () => void
  onUpdateScene: (id: string, data: Partial<Scene>) => void
  projectId?: string
}

export function VoiceoverSection({
  scene,
  sceneIndex,
  onOpenVoicePicker,
  onUpdateScene,
  projectId = '',
}: VoiceoverSectionProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [localText, setLocalText] = useState(scene.text || '')
  const audioRef = useRef<HTMLAudioElement | null>(null)
  // const { user } = useUser()
  const { data: session } = authClient.useSession()

  const gatedVoiceGeneration = useGatedVoiceGeneration(projectId)
  const debouncedUpdateScene = useDebounceSceneUpdate(scene.id, 300)

  // Sync local text with scene text when scene changes
  useEffect(() => {
    setLocalText(scene.text || '')
  }, [scene.text])

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value
    // Update local state immediately for responsive UI
    setLocalText(newText)
    // Debounce the store update to prevent excessive updates
    debouncedUpdateScene({ text: newText })
  }

  const handleGenerateVoiceover = async () => {
    if (!scene.voiceSettings.voiceId || !scene.text) {
      toast.warning('Please select a voice and enter text first')
      return
    }

    const generateVoice = async () => {
      const result = await gatedVoiceGeneration.generateVoice({
        text: scene.text,
        voice_id: scene.voiceSettings.voiceId,
        userId: session?.user?.id, // Pass userId
      })

      // If result is null, it means the limit was reached and upgrade modal was shown
      if (!result) {
        throw new Error('Voice generation limit reached')
      }

      return result
    }

    toast.promise(generateVoice(), {
      loading: 'Generating voiceover...',
      success: result => {
        // Create audio element to get duration
        const audio = new Audio(result.audioUrl)
        audio.onloadedmetadata = () => {
          // Generate captions using the centralized function
          import('@/lib/remotion/utils/captionUtils').then(
            ({ generateCaptionsFromElevenLabs, generateBasicCaptions }) => {
              const captions = result.alignment
                ? generateCaptionsFromElevenLabs(
                    scene.text,
                    result.alignment,
                    audio.duration
                  )
                : generateBasicCaptions(scene.text, audio.duration)

              onUpdateScene(scene.id, {
                voiceSettings: {
                  ...scene.voiceSettings,
                  voiceUrl: result.audioUrl, // Use voiceUrl to match existing schema
                  alignment: result.alignment, // Save alignment data for future use
                },
                // Update voiceover field to ensure new audio is used (has higher priority in Remotion)
                voiceover: {
                  audioUrl: result.audioUrl,
                  audioDuration: audio.duration,
                  volume: scene.voiceSettings.voiceVol || 100,
                  speed: scene.voiceSettings.voiceSpeed || 1,
                },
                duration: audio.duration,
                captions: captions, // Save generated captions
              })

              // Force Remotion player to refresh by seeking to current time
              setTimeout(async () => {
                const { useVideoStore } = await import('@/store/video-store')
                const currentPlayer =
                  useVideoStore.getState().playerRef?.current
                if (currentPlayer) {
                  const currentFrame = currentPlayer.getCurrentFrame() || 0
                  currentPlayer.seekTo(currentFrame)
                }
              }, 100)
            }
          )
        }

        audio.onerror = () => {
          // Even on audio error, try to generate captions if we have alignment
          import('@/lib/remotion/utils/captionUtils').then(
            ({ generateCaptionsFromElevenLabs, generateBasicCaptions }) => {
              const captions = result.alignment
                ? generateCaptionsFromElevenLabs(
                    scene.text,
                    result.alignment,
                    5
                  ) // fallback duration
                : generateBasicCaptions(scene.text, 5)

              onUpdateScene(scene.id, {
                voiceSettings: {
                  ...scene.voiceSettings,
                  voiceUrl: result.audioUrl, // Use voiceUrl to match existing schema
                  alignment: result.alignment,
                },
                // Update voiceover field to ensure new audio is used (has higher priority in Remotion)
                voiceover: {
                  audioUrl: result.audioUrl,
                  audioDuration: 5, // fallback duration
                  volume: scene.voiceSettings.voiceVol || 100,
                  speed: scene.voiceSettings.voiceSpeed || 1,
                },
                captions: captions,
              })

              // Force Remotion player to refresh by seeking to current time
              setTimeout(async () => {
                const { useVideoStore } = await import('@/store/video-store')
                const currentPlayer =
                  useVideoStore.getState().playerRef?.current
                if (currentPlayer) {
                  const currentFrame = currentPlayer.getCurrentFrame() || 0
                  currentPlayer.seekTo(currentFrame)
                }
              }, 100)
            }
          )
        }

        return 'Voiceover generated successfully!'
      },
      error: error => {
        console.error('Failed to generate voiceover:', error)
        return 'Failed to generate voiceover'
      },
    })
  }

  const handlePlayPause = () => {
    if (!scene.voiceSettings.voiceUrl) return

    if (isPlaying) {
      // Pause audio
      if (audioRef.current) {
        audioRef.current.pause()
        setIsPlaying(false)
      }
    } else {
      // Play audio
      if (audioRef.current) {
        audioRef.current.src = scene.voiceSettings.voiceUrl
        audioRef.current.play()
        setIsPlaying(true)

        // Set up event listeners
        audioRef.current.onended = () => setIsPlaying(false)
        audioRef.current.onpause = () => setIsPlaying(false)
      } else {
        // Create new audio element
        const audio = new Audio(scene.voiceSettings.voiceUrl)
        audioRef.current = audio
        audio.play()
        setIsPlaying(true)

        // Set up event listeners
        audio.onended = () => setIsPlaying(false)
        audio.onpause = () => setIsPlaying(false)
      }
    }
  }

  return (
    <div className='space-y-4'>
      {/* Hidden audio element for playback */}
      <audio ref={audioRef} style={{ display: 'none' }} />

      <div className='flex items-center gap-2 mb-2'>
        <Label className='text-sm font-medium'>
          Scene {sceneIndex + 1} • Voiceover
        </Label>
        <Button
          variant='outline'
          size='sm'
          className='h-6 px-2 text-xs gap-1'
          onClick={onOpenVoicePicker}
        >
          <Mic className='w-3 h-3' />
          <span className='w-2 h-2 bg-primary rounded-full'></span>
          {scene.voiceSettings.voiceName || 'Select Voice'}
        </Button>

        {/* Generate Button */}
        {scene.voiceSettings.voiceId && (
          <Button
            variant='outline'
            size='sm'
            className='h-6 w-6 p-0'
            title={
              !gatedVoiceGeneration.voiceRegeneration.allowed
                ? 'Voice regeneration limit reached'
                : gatedVoiceGeneration.isGenerating
                  ? 'Generating...'
                  : 'Generate voiceover'
            }
            onClick={handleGenerateVoiceover}
            disabled={
              gatedVoiceGeneration.isGenerating ||
              !gatedVoiceGeneration.voiceRegeneration.allowed
            }
          >
            <RotateCw
              className={`w-3 h-3 ${gatedVoiceGeneration.isGenerating ? 'animate-spin' : ''}`}
            />
          </Button>
        )}

        {/* Play/Pause Button */}
        {scene.voiceSettings.voiceUrl && (
          <Button
            variant='outline'
            size='sm'
            className='h-6 w-6 p-0'
            title={isPlaying ? 'Pause voiceover' : 'Play voiceover'}
            onClick={handlePlayPause}
          >
            {isPlaying ? (
              <Pause className='w-3 h-3' />
            ) : (
              <Play className='w-3 h-3' />
            )}
          </Button>
        )}

        {/* Duration Badge */}
        {typeof scene.duration === 'number' && (
          <Badge variant='outline' className='h-5 px-1.5 text-[10px]'>
            {formatDuration(scene.duration)}
          </Badge>
        )}
      </div>

      <Textarea
        value={localText}
        onChange={handleTextChange}
        placeholder='Enter the voiceover text for this scene...'
        className='min-h-[120px]'
      />
    </div>
  )
}
