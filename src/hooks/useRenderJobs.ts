'use client'

import { useEffect, useState, useCallback } from 'react'
// import { useUser } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'
import { supabase } from '@/lib/supabase-client'

export interface RenderJob {
  id: string
  projectId: string
  userId: string
  status: 'initializing' | 'rendering' | 'completed' | 'failed'
  progress: number
  publicUrl: string | null
  thumbnailUrl: string | null
  errorMessage: string | null
  renderMethod: string | null
  exportName: string | null
  exportResolution: string | null
  createdAt: Date
  updatedAt: Date
  youtubeId: string | null
}

// Database row type (snake_case fields from Supabase)
interface RenderJobRow {
  id: string
  project_id: string
  user_id: string
  status: string
  progress: number | null
  public_url: string | null
  thumbnail_url: string | null
  error_message: string | null
  render_method: string | null
  export_name: string | null
  export_resolution: string | null
  created_at: string | null
  updated_at: string | null
  youtube_id: string | null
}

// Cache interface for localStorage
interface RenderJobsCacheData {
  data: RenderJob[]
  timestamp: number
  projectId?: string
  userId: string
}

// Cache management utility
class RenderJobsCacheManager {
  private static readonly CACHE_KEY = 'adori_render_jobs_cache'
  private static readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  static get(userId: string, projectId?: string): RenderJob[] | null {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY)
      if (!cached) return null

      const cache: RenderJobsCacheData = JSON.parse(cached)

      // Check if cache is valid
      if (
        cache.userId !== userId ||
        cache.projectId !== projectId ||
        Date.now() - cache.timestamp > this.CACHE_DURATION
      ) {
        this.clear()
        return null
      }

      // Convert date strings back to Date objects
      return cache.data.map(job => ({
        ...job,
        createdAt: new Date(job.createdAt),
        updatedAt: new Date(job.updatedAt),
      }))
    } catch (error) {
      console.error('Error reading render jobs cache:', error)
      this.clear()
      return null
    }
  }

  static set(data: RenderJob[], userId: string, projectId?: string): void {
    try {
      const cache: RenderJobsCacheData = {
        data,
        timestamp: Date.now(),
        projectId,
        userId,
      }
      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache))
    } catch (error) {
      console.error('Error writing render jobs cache:', error)
    }
  }

  static clear(): void {
    try {
      localStorage.removeItem(this.CACHE_KEY)
    } catch (error) {
      console.error('Error clearing render jobs cache:', error)
    }
  }

  static invalidate(userId: string, projectId?: string): void {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY)
      if (!cached) return

      const cache: RenderJobsCacheData = JSON.parse(cached)

      // Only clear if the cache matches the current user/project
      if (cache.userId === userId && cache.projectId === projectId) {
        this.clear()
      }
    } catch (error) {
      console.error('Error invalidating render jobs cache:', error)
    }
  }
}

export function useRenderJobs(projectId?: string) {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()
  const [renderJobs, setRenderJobs] = useState<RenderJob[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [connectionStatus, setConnectionStatus] =
    useState<string>('disconnected')
  const [updateCounter, setUpdateCounter] = useState(0)

  // Normalize database row to RenderJob interface
  const normalizeJob = useCallback(
    (job: RenderJobRow): RenderJob => ({
      id: job.id,
      projectId: job.project_id,
      userId: job.user_id,
      status: job.status as
        | 'initializing'
        | 'rendering'
        | 'completed'
        | 'failed',
      progress: job.progress || 0,
      createdAt: job.created_at ? new Date(job.created_at) : new Date(),
      updatedAt: job.updated_at ? new Date(job.updated_at) : new Date(),
      thumbnailUrl: job.thumbnail_url,
      publicUrl: job.public_url,
      youtubeId: job.youtube_id,
      errorMessage: job.error_message,
      renderMethod: job.render_method,
      exportName: job.export_name,
      exportResolution: job.export_resolution,
    }),
    []
  )

  // Fetch render jobs with caching
  const fetchRenderJobs = useCallback(
    async (useCache = true) => {
      if (!session?.user?.id) return

      console.log('🔧 Fetching render jobs:', {
        userId: session.user.id,
        projectId,
        useCache,
      })

      // Try to get from cache first
      if (useCache) {
        const cachedJobs = RenderJobsCacheManager.get(
          session.user.id,
          projectId
        )
        if (cachedJobs) {
          console.log(
            '📦 ✅ Using cached render jobs (avoiding API call):',
            cachedJobs.length
          )
          setRenderJobs(cachedJobs)
          setIsLoading(false)
          return
        } else {
          console.log('📦 ❌ No valid cache found, will fetch from API')
        }
      }

      try {
        // Use API route instead of direct Supabase call
        const params = new URLSearchParams()
        if (projectId) {
          params.append('projectId', projectId)
        }

        const apiUrl = `/api/render-jobs${params.toString() ? `?${params.toString()}` : ''}`
        console.log('🌐 Calling API:', apiUrl)

        const response = await fetch(apiUrl)

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(
            `HTTP error! status: ${response.status}, message: ${errorData.error || 'Unknown error'}`
          )
        }

        const { data } = await response.json()

        // Normalize DB fields to camelCase for UI (same as before)
        const normalized = ((data as RenderJobRow[]) || []).map(normalizeJob)

        setRenderJobs(normalized)

        // Cache the results
        RenderJobsCacheManager.set(normalized, session.user.id, projectId)

        console.log(
          '🔄 Fetched and cached render jobs via API:',
          normalized.length
        )
      } catch (error) {
        console.error('Error fetching render jobs:', error)
      } finally {
        setIsLoading(false)
      }
    },
    [session?.user?.id, projectId, normalizeJob]
  )

  // Initial data fetch
  useEffect(() => {
    if (!session?.user?.id) return

    console.log('🔧 useRenderJobs effect running with:', {
      userId: session.user.id,
      projectId,
    })

    fetchRenderJobs(true)

    // Set up real-time subscription
    const channel = supabase
      .channel('render_jobs_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'render_jobs',
          filter: `user_id=eq.${session.user.id}`,
        },
        payload => {
          const newJobData = payload.new as RenderJobRow | null
          console.log('🔄 Real-time update received:', {
            eventType: payload.eventType,
            table: payload.table,
            schema: payload.schema,
            jobId: newJobData?.id,
            status: newJobData?.status,
            progress: newJobData?.progress,
            hasOld: !!payload.old,
          })

          // Log the raw payload.new to see exact field names
          if (payload.new) {
            console.log('📋 Raw payload.new fields:', Object.keys(payload.new))
            console.log('📋 Raw payload.new data:', payload.new)
          }

          // Debug: Check if we're getting INSERT when we should get UPDATE
          if (payload.eventType === 'INSERT' && newJobData?.id) {
            console.log(
              '🔍 DEBUG: Checking if INSERT job already exists in state:',
              newJobData.id
            )
          }

          if (payload.eventType === 'INSERT') {
            const newJob = normalizeJob(payload.new as RenderJobRow)
            console.log('📝 INSERT: Adding new job:', newJob.id, newJob.status)
            // Only add if it matches the project filter (if any)
            if (!projectId || newJob.projectId === projectId) {
              setRenderJobs(prev => {
                // Check if job already exists to prevent duplicates
                const existingJobIndex = prev.findIndex(
                  job => job.id === newJob.id
                )
                if (existingJobIndex !== -1) {
                  console.log(
                    '🔄 INSERT: Job already exists, treating as UPDATE:',
                    newJob.id
                  )
                  // Update existing job instead of adding duplicate
                  const updated = prev.map(job =>
                    job.id === newJob.id ? newJob : job
                  )
                  console.log(
                    '🔄 State updated (existing job), job count:',
                    updated.length,
                    'updated job:',
                    newJob
                  )
                  // Update cache with the latest data for progress updates
                  RenderJobsCacheManager.set(
                    updated,
                    session.user.id,
                    projectId
                  )
                  setUpdateCounter(c => c + 1) // Force re-render
                  return updated
                } else {
                  // Add new job
                  const updated = [newJob, ...prev]
                  console.log(
                    '📝 State updated (new job), job count:',
                    updated.length,
                    'new job:',
                    newJob
                  )
                  // Invalidate cache only when adding truly new jobs
                  RenderJobsCacheManager.invalidate(session.user.id, projectId)
                  setUpdateCounter(c => c + 1) // Force re-render
                  return updated
                }
              })
            } else {
              console.log(
                '🚫 New job filtered out due to project mismatch:',
                newJob.projectId,
                'vs',
                projectId
              )
            }
          } else if (payload.eventType === 'UPDATE') {
            const updatedJob = normalizeJob(payload.new as RenderJobRow)
            console.log(
              '🔄 UPDATE: Updating job:',
              updatedJob.id,
              'status:',
              updatedJob.status,
              'progress:',
              updatedJob.progress
            )
            // Only update if it matches the project filter (if any)
            if (!projectId || updatedJob.projectId === projectId) {
              setRenderJobs(prev => {
                const existingJobIndex = prev.findIndex(
                  job => job.id === updatedJob.id
                )
                if (existingJobIndex !== -1) {
                  // Update existing job
                  const updated = prev.map(job =>
                    job.id === updatedJob.id ? updatedJob : job
                  )
                  console.log(
                    '🔄 State updated (existing job), job count:',
                    updated.length,
                    'updated job:',
                    updatedJob
                  )
                  // Update cache with the latest data for progress updates
                  RenderJobsCacheManager.set(
                    updated,
                    session.user.id,
                    projectId
                  )
                  setUpdateCounter(c => c + 1) // Force re-render
                  return updated
                } else {
                  // Job doesn't exist yet, add it (race condition handling)
                  console.log(
                    '🔄 UPDATE: Job not found in state, adding it:',
                    updatedJob.id
                  )
                  const updated = [updatedJob, ...prev]
                  console.log(
                    '🔄 State updated (new job from UPDATE), job count:',
                    updated.length,
                    'new job:',
                    updatedJob
                  )
                  // Invalidate cache only when adding new jobs
                  RenderJobsCacheManager.invalidate(session.user.id, projectId)
                  setUpdateCounter(c => c + 1) // Force re-render
                  return updated
                }
              })
            } else {
              console.log(
                '🚫 Job filtered out due to project mismatch:',
                updatedJob.projectId,
                'vs',
                projectId
              )
            }
          } else if (payload.eventType === 'DELETE') {
            console.log('🗑️ DELETE: Removing job:', payload.old.id)
            setRenderJobs(prev => {
              const updated = prev.filter(job => job.id !== payload.old.id)
              // Invalidate cache when jobs are deleted
              RenderJobsCacheManager.invalidate(session.user.id, projectId)
              setUpdateCounter(c => c + 1) // Force re-render
              return updated
            })
          }
        }
      )
      .subscribe(status => {
        console.log('Supabase subscription status:', status)
        setConnectionStatus(status)
        if (status === 'SUBSCRIBED') {
          console.log(
            '✅ Successfully subscribed to render_jobs changes for user:',
            session.user.id
          )
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Error subscribing to render_jobs changes')
          // Try to reconnect after a short delay
          setTimeout(() => {
            console.log('🔄 Attempting to reconnect...')
            channel.unsubscribe()
            // The useEffect will re-run and create a new subscription
          }, 2000)
        } else if (status === 'TIMED_OUT') {
          console.error('⏰ Subscription to render_jobs timed out')
        } else if (status === 'CLOSED') {
          console.log('🔒 Subscription to render_jobs closed')
        }
      })

    return () => {
      channel.unsubscribe()
    }
  }, [session?.user?.id, projectId, fetchRenderJobs, normalizeJob])

  // Manual refresh function that bypasses cache
  const refreshJobs = useCallback(async () => {
    if (!session?.user?.id) return
    setIsLoading(true)
    await fetchRenderJobs(false) // Force fresh fetch
  }, [session?.user?.id, fetchRenderJobs])

  return {
    renderJobs,
    isLoading,
    refreshJobs,
    connectionStatus,
    updateCounter,
  }
}

// Paginated render jobs for /my-videos with real-time updates
export function usePaginatedRenderJobs(page = 1, limit = 8) {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()
  const [jobs, setJobs] = useState<RenderJob[]>([])
  const [total, setTotal] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [connectionStatus, setConnectionStatus] =
    useState<string>('disconnected')
  const [updateCounter, setUpdateCounter] = useState(0)

  // Normalize database row to RenderJob interface
  const normalizeJob = useCallback(
    (job: RenderJobRow): RenderJob => ({
      id: job.id,
      projectId: job.project_id,
      userId: job.user_id,
      status: job.status as
        | 'initializing'
        | 'rendering'
        | 'completed'
        | 'failed',
      progress: job.progress || 0,
      createdAt: job.created_at ? new Date(job.created_at) : new Date(),
      updatedAt: job.updated_at ? new Date(job.updated_at) : new Date(),
      thumbnailUrl: job.thumbnail_url,
      publicUrl: job.public_url,
      youtubeId: job.youtube_id,
      errorMessage: job.error_message,
      renderMethod: job.render_method,
      exportName: job.export_name,
      exportResolution: job.export_resolution,
    }),
    []
  )

  // Fetch paginated jobs
  const fetchJobs = useCallback(async () => {
    if (!session?.user?.id) return

    try {
      // Use API route instead of direct Supabase call
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      })

      const apiUrl = `/api/render-jobs/paginated?${params.toString()}`
      console.log('🌐 Calling paginated API:', apiUrl)

      const response = await fetch(apiUrl)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('❌ API error:', errorData)
        setJobs([])
        setTotal(0)
        return
      }

      const { data, total } = await response.json()

      // Set total count
      setTotal(total || 0)

      // Normalize and set jobs (same as before)
      const normalized = ((data as RenderJobRow[]) || []).map(normalizeJob)
      setJobs(normalized)

      console.log('✅ Fetched paginated jobs via API:', {
        count: normalized.length,
        total: total || 0,
        page,
        limit,
      })
    } catch (error) {
      console.error('Error fetching paginated jobs:', error)
      setJobs([])
      setTotal(0)
    } finally {
      setIsLoading(false)
    }
  }, [session?.user?.id, page, limit, normalizeJob])

  // Initial data fetch and real-time subscription
  useEffect(() => {
    if (!session?.user?.id) return

    setIsLoading(true)
    fetchJobs()

    // Set up real-time subscription for all user's jobs
    const channel = supabase
      .channel('paginated_render_jobs_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'render_jobs',
          filter: `user_id=eq.${session.user.id}`,
        },
        payload => {
          const newJobData = payload.new as RenderJobRow | null
          console.log('🔄 Paginated real-time update received:', {
            eventType: payload.eventType,
            jobId: newJobData?.id,
            status: newJobData?.status,
            progress: newJobData?.progress,
          })

          if (payload.eventType === 'INSERT' && newJobData) {
            const newJob = normalizeJob(newJobData)
            console.log(
              '📝 INSERT: Adding new job to paginated view:',
              newJob.id
            )

            // Add new job to the beginning of the list
            setJobs(prev => {
              const existingJobIndex = prev.findIndex(
                job => job.id === newJob.id
              )
              if (existingJobIndex !== -1) {
                // Update existing job instead of adding duplicate
                const updated = prev.map(job =>
                  job.id === newJob.id ? newJob : job
                )
                console.log(
                  '🔄 INSERT: Job already exists, treating as UPDATE:',
                  newJob.id
                )
                setUpdateCounter(c => c + 1)
                return updated
              } else {
                // Add new job and increment total
                setTotal(prev => prev + 1)
                const updated = [newJob, ...prev.slice(0, limit - 1)] // Keep within page limit
                console.log('📝 INSERT: Added new job, total now:', total + 1)
                setUpdateCounter(c => c + 1)
                return updated
              }
            })
          } else if (payload.eventType === 'UPDATE' && newJobData) {
            const updatedJob = normalizeJob(newJobData)
            console.log(
              '🔄 UPDATE: Updating job in paginated view:',
              updatedJob.id,
              'status:',
              updatedJob.status,
              'progress:',
              updatedJob.progress
            )

            setJobs(prev => {
              const existingJobIndex = prev.findIndex(
                job => job.id === updatedJob.id
              )
              if (existingJobIndex !== -1) {
                // Update existing job
                const updated = prev.map(job =>
                  job.id === updatedJob.id ? updatedJob : job
                )
                console.log('🔄 UPDATE: Updated existing job:', updatedJob.id)
                setUpdateCounter(c => c + 1)
                return updated
              } else {
                // Job not in current page, but might need to be added if it's recent
                console.log(
                  '🔄 UPDATE: Job not in current page:',
                  updatedJob.id
                )
                return prev
              }
            })
          } else if (payload.eventType === 'DELETE') {
            console.log(
              '🗑️ DELETE: Removing job from paginated view:',
              payload.old.id
            )
            setJobs(prev => {
              const updated = prev.filter(job => job.id !== payload.old.id)
              if (updated.length !== prev.length) {
                setTotal(prev => Math.max(0, prev - 1))
                console.log('🗑️ DELETE: Removed job, total now:', total - 1)
              }
              setUpdateCounter(c => c + 1)
              return updated
            })
          }
        }
      )
      .subscribe(status => {
        console.log('Paginated Supabase subscription status:', status)
        setConnectionStatus(status)
        if (status === 'SUBSCRIBED') {
          console.log(
            '✅ Successfully subscribed to paginated render_jobs changes for user:',
            session.user.id
          )
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Error subscribing to paginated render_jobs changes')
        }
      })

    return () => {
      channel.unsubscribe()
    }
  }, [session?.user?.id, page, limit, fetchJobs, normalizeJob, total])

  const refreshJobs = useCallback(() => {
    setIsLoading(true)
    fetchJobs()
  }, [fetchJobs])

  return {
    jobs,
    total,
    isLoading,
    refreshJobs,
    connectionStatus,
    updateCounter,
  }
}
