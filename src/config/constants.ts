/**
 * Application-wide constants
 *
 * This file contains constants that are used throughout the application.
 * Centralizing these values makes it easier to maintain and update them.
 */

// Breakpoints
export const MOBILE_BREAKPOINT = 768 // in pixels

// Other constants can be added here as needed
export const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
export const SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp']

/**
 * Stripe Plans Configuration
 *
 * This section contains all plan definitions, pricing, and feature configurations.
 * Update this when adding new plans or changing existing ones.
 */

export const plans = [
  {
    id: 1,
    name: 'free',
    period: 'forever',
    priceId: process.env.STRIPE_FREE_PLAN_PRICE_ID!,
    limits: {
      projects: 3,
      videoExports: 0,
      aiImages: 20,
      storage: 100, // 100MB
      teamMembers: 1,
      voiceRegenerations: 3, // per project
      videoDuration: 60, // 1 minute in seconds
      podcastDuration: 30 * 60, // 30 minutes in seconds
    },
    price: 0,
    features: [],
    gatedFeatures: {
      videoExport: true,
      videoPublishing: true,
    },
  },
  {
    id: 2,
    name: 'basic',
    period: 'monthly',
    priceId: process.env.STRIPE_BASIC_PLAN_PRICE_ID!,
    annualDiscountPriceId:
      process.env.STRIPE_BASIC_PLAN_ANNUAL_DISCOUNT_PRICE_ID!,
    limits: {
      projects: 10,
      videoExports: 10,
      aiImages: 100,
      storage: 500, // 500MB
      teamMembers: 1,
      voiceRegenerations: 10, // per project
      videoDuration: 180, // 3 minutes in seconds
      podcastDuration: 60 * 60, // 1 hour in seconds
    },
    price: 15,
    features: ['full HD exports'],
    gatedFeatures: {
      videoExport: false,
      videoPublishing: true,
    },
  },
  {
    id: 3,
    name: 'basic',
    period: 'annual',
    priceId: process.env.STRIPE_BASIC_PLAN_ANNUAL_DISCOUNT_PRICE_ID!,
    annualDiscountPriceId:
      process.env.STRIPE_BASIC_PLAN_ANNUAL_DISCOUNT_PRICE_ID!,
    limits: {
      projects: 120,
      videoExports: 120,
      aiImages: 1200,
      storage: 6144,
      teamMembers: 1,
    },
    price: 144,
    features: [
      '720p videoexports',
      '3 min video script limit',
      'upto 1 hr Podcast',
    ],
  },
  {
    id: 4,
    name: 'premium',
    period: 'monthly',
    priceId: process.env.STRIPE_PREMIUM_PLAN_PRICE_ID!,
    annualDiscountPriceId:
      process.env.STRIPE_PREMIUM_PLAN_ANNUAL_DISCOUNT_PRICE_ID!,
    limits: {
      projects: 20,
      videoExports: 20,
      aiImages: 200,
      storage: 1024, // 1GB
      teamMembers: 5,
      voiceRegenerations: 20, // per project
      videoDuration: 300, // 5 minutes in seconds
      podcastDuration: 120 * 60, // 2 hours in seconds
    },
    price: 45,
    features: ['Publish to Youtube', 'Invite team members'],
    gatedFeatures: {
      videoExport: false,
      videoPublishing: false,
    },
  },
]
