-- Remove the old userId column and its index
DROP INDEX IF EXISTS "idx_user_usage_user_id";
ALTER TABLE "usage" DROP COLUMN "user_id";

-- Add the new members array column
ALTER TABLE "usage" ADD COLUMN "members" text[] NOT NULL DEFAULT '{}';

-- Create new indexes for organization_id and members
CREATE INDEX "idx_usage_organization_id" ON "usage"("organization_id");
CREATE INDEX "idx_usage_members" ON "usage" USING GIN("members");
