CREATE TABLE "youtube_connections" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" text NOT NULL,
	"access_token" text NOT NULL,
	"refresh_token" text NOT NULL,
	"expires_at" timestamp with time zone NOT NULL,
	"channel_id" text NOT NULL,
	"channel_title" text NOT NULL,
	"channel_thumbnail_url" text,
	"channel_description" text,
	"scopes" text[] NOT NULL,
	"connected_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"is_active" boolean DEFAULT true NOT NULL,
	CONSTRAINT "youtube_connections_user_id_unique" UNIQUE("user_id")
);
--> statement-breakpoint
CREATE INDEX "idx_youtube_connections_user_id" ON "youtube_connections" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "idx_youtube_connections_channel_id" ON "youtube_connections" USING btree ("channel_id");--> statement-breakpoint
CREATE INDEX "idx_youtube_connections_expires_at" ON "youtube_connections" USING btree ("expires_at");