CREATE TABLE "subscription" (
	"id" text PRIMARY KEY NOT NULL,
	"plan" text NOT NULL,
	"reference_id" text NOT NULL,
	"stripe_customer_id" text,
	"stripe_subscription_id" text,
	"status" text DEFAULT 'incomplete',
	"period_start" timestamp,
	"period_end" timestamp,
	"cancel_at_period_end" boolean,
	"seats" integer,
	"trial_start" timestamp,
	"trial_end" timestamp
);
--> statement-breakpoint
DROP INDEX "idx_user_usage_user_id";--> statement-breakpoint
ALTER TABLE "usage" ADD COLUMN "members" text[] NOT NULL;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "stripe_customer_id" text;--> statement-breakpoint
ALTER TABLE "youtube_connections" ADD COLUMN "organization_id" text;--> statement-breakpoint
CREATE INDEX "idx_subscription_stripe_customer_id" ON "subscription" USING btree ("stripe_customer_id");--> statement-breakpoint
CREATE INDEX "idx_usage_organization_id" ON "usage" USING btree ("organization_id");--> statement-breakpoint
CREATE INDEX "idx_usage_members" ON "usage" USING btree ("members");--> statement-breakpoint
ALTER TABLE "usage" DROP COLUMN "user_id";