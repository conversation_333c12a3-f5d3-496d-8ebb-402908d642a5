{"id": "ce352578-acab-418e-9000-a14e656568eb", "prevId": "576f4eaf-649c-45a4-b113-08916919590d", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"idx_account_user_id": {"name": "idx_account_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_keys": {"name": "api_keys", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'"}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"api_keys_key_unique": {"name": "api_keys_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitation": {"name": "invitation", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "inviter_id": {"name": "inviter_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"idx_invitation_email": {"name": "idx_invitation_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_invitation_organization_id": {"name": "idx_invitation_organization_id", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"invitation_organization_id_organization_id_fk": {"name": "invitation_organization_id_organization_id_fk", "tableFrom": "invitation", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "invitation_inviter_id_user_id_fk": {"name": "invitation_inviter_id_user_id_fk", "tableFrom": "invitation", "tableTo": "user", "columnsFrom": ["inviter_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media_assets": {"name": "media_assets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "original_name": {"name": "original_name", "type": "text", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "original_url": {"name": "original_url", "type": "text", "primaryKey": false, "notNull": true}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "low_res_url": {"name": "low_res_url", "type": "text", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "integer", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "integer", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "numeric(10, 3)", "primaryKey": false, "notNull": false}, "quality": {"name": "quality", "type": "text", "primaryKey": false, "notNull": false}, "fps": {"name": "fps", "type": "integer", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_media_assets_user_id": {"name": "idx_media_assets_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_media_assets_mime_type": {"name": "idx_media_assets_mime_type", "columns": [{"expression": "mime_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_media_assets_created_at": {"name": "idx_media_assets_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.member": {"name": "member", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'member'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"idx_member_user_id": {"name": "idx_member_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_member_organization_id": {"name": "idx_member_organization_id", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"member_organization_id_organization_id_fk": {"name": "member_organization_id_organization_id_fk", "tableFrom": "member", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "member_user_id_user_id_fk": {"name": "member_user_id_user_id_fk", "tableFrom": "member", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization": {"name": "organization", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_organization_slug": {"name": "idx_organization_slug", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organization_slug_unique": {"name": "organization_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"project_id": {"name": "project_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": false}, "project_name": {"name": "project_name", "type": "text", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "cover_color": {"name": "cover_color", "type": "text", "primaryKey": false, "notNull": false}, "cover_pic": {"name": "cover_pic", "type": "text", "primaryKey": false, "notNull": false}, "orientation": {"name": "orientation", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "numeric(6, 3)", "primaryKey": false, "notNull": false}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false}, "voice_regenerations": {"name": "voice_regenerations", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "speech": {"name": "speech", "type": "jsonb", "primaryKey": false, "notNull": false}, "background_video": {"name": "background_video", "type": "jsonb", "primaryKey": false, "notNull": false}, "music": {"name": "music", "type": "jsonb", "primaryKey": false, "notNull": false}, "caption_settings": {"name": "caption_settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "scenes": {"name": "scenes", "type": "jsonb", "primaryKey": false, "notNull": false}, "event_id": {"name": "event_id", "type": "text", "primaryKey": false, "notNull": false}, "run_id": {"name": "run_id", "type": "text", "primaryKey": false, "notNull": false}, "blog_images": {"name": "blog_images", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"idx_projects_user_id": {"name": "idx_projects_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_projects_organization_id": {"name": "idx_projects_organization_id", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.render_jobs": {"name": "render_jobs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "progress": {"name": "progress", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "public_url": {"name": "public_url", "type": "text", "primaryKey": false, "notNull": false}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "render_method": {"name": "render_method", "type": "text", "primaryKey": false, "notNull": false}, "export_name": {"name": "export_name", "type": "text", "primaryKey": false, "notNull": false}, "export_resolution": {"name": "export_resolution", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "youtube_id": {"name": "youtube_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_render_jobs_user_project": {"name": "idx_render_jobs_user_project", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "project_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_render_jobs_organization_id": {"name": "idx_render_jobs_organization_id", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_render_jobs_status": {"name": "idx_render_jobs_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_render_jobs_created_at": {"name": "idx_render_jobs_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "active_organization_id": {"name": "active_organization_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_session_user_id": {"name": "idx_session_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_session_token": {"name": "idx_session_token", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stock_music": {"name": "stock_music", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "genre": {"name": "genre", "type": "text", "primaryKey": false, "notNull": true}, "mood": {"name": "mood", "type": "text", "primaryKey": false, "notNull": true}, "artist_name": {"name": "artist_name", "type": "text", "primaryKey": false, "notNull": true}, "artist_url": {"name": "artist_url", "type": "text", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "license_id": {"name": "license_id", "type": "text", "primaryKey": false, "notNull": true}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": false}, "preview_url": {"name": "preview_url", "type": "text", "primaryKey": false, "notNull": true}, "duration_millis": {"name": "duration_millis", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription": {"name": "subscription", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "plan": {"name": "plan", "type": "text", "primaryKey": false, "notNull": true}, "reference_id": {"name": "reference_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'incomplete'"}, "period_start": {"name": "period_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "period_end": {"name": "period_end", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "boolean", "primaryKey": false, "notNull": false}, "seats": {"name": "seats", "type": "integer", "primaryKey": false, "notNull": false}, "trial_start": {"name": "trial_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "trial_end": {"name": "trial_end", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"idx_subscription_stripe_customer_id": {"name": "idx_subscription_stripe_customer_id", "columns": [{"expression": "stripe_customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.usage": {"name": "usage", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "members": {"name": "members", "type": "text[]", "primaryKey": false, "notNull": true}, "projects_used": {"name": "projects_used", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "video_exports_used": {"name": "video_exports_used", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "ai_images_used": {"name": "ai_images_used", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "team_members_used": {"name": "team_members_used", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "storage_used": {"name": "storage_used", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "plan_type": {"name": "plan_type", "type": "text", "primaryKey": false, "notNull": true}, "plan_status": {"name": "plan_status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "current_period_start": {"name": "current_period_start", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "current_period_end": {"name": "current_period_end", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_usage_organization_id": {"name": "idx_usage_organization_id", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_usage_members": {"name": "idx_usage_members", "columns": [{"expression": "members", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "normalized_email": {"name": "normalized_email", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"idx_user_email": {"name": "idx_user_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "user_normalized_email_unique": {"name": "user_normalized_email_unique", "nullsNotDistinct": false, "columns": ["normalized_email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"idx_verification_identifier": {"name": "idx_verification_identifier", "columns": [{"expression": "identifier", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.youtube_connections": {"name": "youtube_connections", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "channel_id": {"name": "channel_id", "type": "text", "primaryKey": false, "notNull": true}, "channel_title": {"name": "channel_title", "type": "text", "primaryKey": false, "notNull": true}, "channel_thumbnail_url": {"name": "channel_thumbnail_url", "type": "text", "primaryKey": false, "notNull": false}, "channel_description": {"name": "channel_description", "type": "text", "primaryKey": false, "notNull": false}, "scopes": {"name": "scopes", "type": "text[]", "primaryKey": false, "notNull": true}, "connected_at": {"name": "connected_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {"idx_youtube_connections_user_id": {"name": "idx_youtube_connections_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_youtube_connections_channel_id": {"name": "idx_youtube_connections_channel_id", "columns": [{"expression": "channel_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_youtube_connections_expires_at": {"name": "idx_youtube_connections_expires_at", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_youtube_connections_user_active_expires": {"name": "idx_youtube_connections_user_active_expires", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}