-- Safe migration to create youtube_connections table if it doesn't exist
-- This migration will not affect any existing tables or data

-- Create the youtube_connections table only if it doesn't exist
CREATE TABLE IF NOT EXISTS "youtube_connections" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" text NOT NULL,
	"access_token" text NOT NULL,
	"refresh_token" text NOT NULL,
	"expires_at" timestamp with time zone NOT NULL,
	"channel_id" text NOT NULL,
	"channel_title" text NOT NULL,
	"channel_thumbnail_url" text,
	"channel_description" text,
	"scopes" text[] NOT NULL,
	"connected_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	"is_active" boolean DEFAULT true NOT NULL
);

-- Create indexes only if they don't exist
CREATE INDEX IF NOT EXISTS "idx_youtube_connections_user_id" ON "youtube_connections" USING btree ("user_id");
CREATE INDEX IF NOT EXISTS "idx_youtube_connections_channel_id" ON "youtube_connections" USING btree ("channel_id");
CREATE INDEX IF NOT EXISTS "idx_youtube_connections_expires_at" ON "youtube_connections" USING btree ("expires_at");

-- Note: We intentionally do NOT add the unique constraint on user_id 
-- as it was removed in migration 0011 to allow multiple connections per user
